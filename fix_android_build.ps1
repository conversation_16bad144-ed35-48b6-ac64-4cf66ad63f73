# Android Build Environment Fix Script
# This script helps diagnose and fix common Android build issues

Write-Host "=== Android Build Environment Diagnostic ===" -ForegroundColor Green
Write-Host ""

# Function to check if a path exists
function Test-PathExists {
    param($Path, $Description)
    if (Test-Path $Path) {
        Write-Host "✓ $Description found at: $Path" -ForegroundColor Green
        return $true
    } else {
        Write-Host "✗ $Description not found at: $Path" -ForegroundColor Red
        return $false
    }
}

# Check Flutter installation
Write-Host "1. Checking Flutter installation..." -ForegroundColor Yellow
try {
    $flutterVersion = flutter --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Flutter is installed" -ForegroundColor Green
    } else {
        Write-Host "✗ Flutter not found in PATH" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Flutter not found" -ForegroundColor Red
}

# Check Java installation
Write-Host "`n2. Checking Java installation..." -ForegroundColor Yellow
$javaFound = $false

# Check common Java locations
$javaPaths = @(
    "C:\Program Files\Android\Android Studio\jbr",
    "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot",
    "C:\Program Files\Java\jdk-11*",
    "C:\Program Files\Java\jdk1.8*"
)

foreach ($path in $javaPaths) {
    $expandedPaths = Get-ChildItem $path -ErrorAction SilentlyContinue
    foreach ($javaPath in $expandedPaths) {
        $javaExe = Join-Path $javaPath.FullName "bin\java.exe"
        if (Test-Path $javaExe) {
            Write-Host "✓ Java found at: $($javaPath.FullName)" -ForegroundColor Green
            $env:JAVA_HOME = $javaPath.FullName
            $env:PATH = "$($javaPath.FullName)\bin;$env:PATH"
            $javaFound = $true
            break
        }
    }
    if ($javaFound) { break }
}

if (-not $javaFound) {
    Write-Host "✗ Java JDK not found!" -ForegroundColor Red
    Write-Host "  Please install OpenJDK 11 from https://adoptium.net/" -ForegroundColor Yellow
    Write-Host "  Or install Android Studio which includes a JDK" -ForegroundColor Yellow
}

# Check Android SDK
Write-Host "`n3. Checking Android SDK..." -ForegroundColor Yellow
$androidSdkPath = "D:\Tools\android-sdk"
if (Test-PathExists $androidSdkPath "Android SDK") {
    $env:ANDROID_HOME = $androidSdkPath
    $env:PATH = "$androidSdkPath\tools;$androidSdkPath\platform-tools;$env:PATH"
}

# Test Java version if found
if ($javaFound) {
    Write-Host "`n4. Testing Java version..." -ForegroundColor Yellow
    try {
        $javaVersionOutput = & java -version 2>&1
        Write-Host "✓ Java version check successful" -ForegroundColor Green
        Write-Host $javaVersionOutput[0] -ForegroundColor Cyan
    } catch {
        Write-Host "✗ Java version check failed" -ForegroundColor Red
    }
}

# Clean and rebuild
Write-Host "`n5. Cleaning project..." -ForegroundColor Yellow
try {
    flutter clean
    Write-Host "✓ Flutter clean completed" -ForegroundColor Green
} catch {
    Write-Host "✗ Flutter clean failed" -ForegroundColor Red
}

Write-Host "`n=== Environment Setup Complete ===" -ForegroundColor Green
Write-Host ""

if ($javaFound) {
    Write-Host "You can now try building with:" -ForegroundColor Yellow
    Write-Host "  flutter build apk --debug" -ForegroundColor Cyan
} else {
    Write-Host "Please install Java JDK first, then run this script again." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
