import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import '../screens/watermark_editor_screen.dart';
import '../theme/app_theme.dart';
import 'modern_card.dart';

class WatermarkControls extends StatelessWidget {
  final WatermarkType watermarkType;
  final String text;
  final double opacity;
  final double fontSize;
  final Offset position;
  final Color textColor;
  final Function(WatermarkType) onTypeChanged;
  final Function(String) onTextChanged;
  final Function(double) onOpacityChanged;
  final Function(double) onFontSizeChanged;
  final Function(Offset) onPositionChanged;
  final Function(Color) onColorChanged;

  const WatermarkControls({
    super.key,
    required this.watermarkType,
    required this.text,
    required this.opacity,
    required this.fontSize,
    required this.position,
    required this.textColor,
    required this.onTypeChanged,
    required this.onTextChanged,
    required this.onOpacityChanged,
    required this.onFontSizeChanged,
    required this.onPositionChanged,
    required this.onColorChanged,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      children: [
        // 标题
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          child: Row(
            children: [
              const Icon(
                Icons.tune,
                color: AppTheme.primaryColor,
                size: 24,
              ),
              const SizedBox(width: AppTheme.spacingS),
              Text(
                '水印设置',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),

        // 控制面板内容
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
            child: Column(
              children: [
                // 水印类型选择
                _buildTypeSelectionCard(l10n),
                const SizedBox(height: AppTheme.spacingM),

                // 文字输入（仅文字水印显示）
                if (watermarkType == WatermarkType.text)
                  _buildTextInputCard(l10n),

                // 样式控制
                _buildStyleControlCard(l10n),
                const SizedBox(height: AppTheme.spacingM),

                // 位置控制
                _buildPositionControlCard(l10n),
                const SizedBox(height: AppTheme.spacingL),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTypeSelectionCard(AppLocalizations l10n) {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.category_outlined,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              SizedBox(width: AppTheme.spacingS),
              Text(
                '水印类型',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),
          Row(
            children: [
              Expanded(
                child: _buildTypeButton(
                  icon: Icons.text_fields,
                  label: '文字水印',
                  isSelected: watermarkType == WatermarkType.text,
                  onTap: () => onTypeChanged(WatermarkType.text),
                ),
              ),
              const SizedBox(width: AppTheme.spacingS),
              Expanded(
                child: _buildTypeButton(
                  icon: Icons.image_outlined,
                  label: '日期水印',
                  isSelected: watermarkType == WatermarkType.date,
                  onTap: () => onTypeChanged(WatermarkType.date),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTypeButton({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: AppTheme.spacingM,
          horizontal: AppTheme.spacingS,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withValues(alpha: 0.1)
              : AppTheme.backgroundColor,
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : AppTheme.textTertiary,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondary,
              size: 24,
            ),
            const SizedBox(height: AppTheme.spacingXS),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextInputCard(AppLocalizations l10n) {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.edit_outlined,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              SizedBox(width: AppTheme.spacingS),
              Text(
                '文字内容',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),
          TextField(
            onChanged: onTextChanged,
            decoration: InputDecoration(
              hintText: l10n.enterText,
              prefixIcon: const Icon(Icons.text_fields),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStyleControlCard(AppLocalizations l10n) {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.palette_outlined,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              SizedBox(width: AppTheme.spacingS),
              Text(
                '样式设置',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),

          // 透明度控制
          _buildSliderControl(
            label: '透明度',
            value: opacity,
            min: 0.1,
            max: 1.0,
            divisions: 9,
            onChanged: onOpacityChanged,
            valueFormatter: (value) => '${(value * 100).round()}%',
          ),

          const SizedBox(height: AppTheme.spacingM),

          // 字体大小控制
          if (watermarkType == WatermarkType.text)
            _buildSliderControl(
              label: '字体大小',
              value: fontSize,
              min: 12.0,
              max: 48.0,
              divisions: 18,
              onChanged: onFontSizeChanged,
              valueFormatter: (value) => '${value.round()}px',
            ),

          if (watermarkType == WatermarkType.text) ...[
            const SizedBox(height: AppTheme.spacingM),
            // 颜色选择
            const Text(
              '颜色',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            _buildColorSelector(),
          ],
        ],
      ),
    );
  }

  Widget _buildSliderControl({
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
    required String Function(double) valueFormatter,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            Text(
              valueFormatter(value),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingS),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          onChanged: onChanged,
          activeColor: AppTheme.primaryColor,
          inactiveColor: AppTheme.primaryColor.withValues(alpha: 0.3),
        ),
      ],
    );
  }

  Widget _buildPositionControlCard(AppLocalizations l10n) {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.place_outlined,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              SizedBox(width: AppTheme.spacingS),
              Text(
                '位置设置',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),

          // 位置预设
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 3,
            mainAxisSpacing: AppTheme.spacingS,
            crossAxisSpacing: AppTheme.spacingS,
            childAspectRatio: 2,
            children: [
              _buildPositionButton('左上', const Offset(0.1, 0.1)),
              _buildPositionButton('中上', const Offset(0.5, 0.1)),
              _buildPositionButton('右上', const Offset(0.9, 0.1)),
              _buildPositionButton('左中', const Offset(0.1, 0.5)),
              _buildPositionButton('中心', const Offset(0.5, 0.5)),
              _buildPositionButton('右中', const Offset(0.9, 0.5)),
              _buildPositionButton('左下', const Offset(0.1, 0.9)),
              _buildPositionButton('中下', const Offset(0.5, 0.9)),
              _buildPositionButton('右下', const Offset(0.9, 0.9)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPositionButton(String label, Offset pos) {
    final bool isSelected = (position.dx - pos.dx).abs() < 0.1 &&
                           (position.dy - pos.dy).abs() < 0.1;

    return GestureDetector(
      onTap: () => onPositionChanged(pos),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withValues(alpha: 0.1)
              : AppTheme.backgroundColor,
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : AppTheme.textTertiary,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        ),
        child: Center(
          child: Text(
            label,
            style: TextStyle(
              color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondary,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              fontSize: 12,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildColorSelector() {
    final List<Color> colors = [
      Colors.white,
      Colors.black,
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.yellow,
      Colors.purple,
      Colors.orange,
    ];

    return Row(
      children: colors.map((color) {
        final bool isSelected = textColor == color;
        return GestureDetector(
          onTap: () => onColorChanged(color),
          child: Container(
            width: 32,
            height: 32,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.grey[300]!,
                width: isSelected ? 3 : 1,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}