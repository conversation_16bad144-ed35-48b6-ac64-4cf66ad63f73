import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/image_service.dart';
import '../widgets/image_source_dialog.dart';
import '../widgets/modern_button.dart';
import '../widgets/modern_card.dart';
import '../theme/app_theme.dart';
import 'watermark_editor_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ImageService _imageService = ImageService();

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.appTitle,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () => _showSettingsDialog(context),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
              foregroundColor: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(width: AppTheme.spacingM),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 欢迎区域
                _buildWelcomeSection(context, l10n),
                const SizedBox(height: AppTheme.spacingXL),

                // 功能卡片
                _buildFeatureCards(context, l10n),
                const SizedBox(height: AppTheme.spacingXL),

                // 快速操作
                _buildQuickActions(context, l10n),
                const SizedBox(height: AppTheme.spacingL),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context, AppLocalizations l10n) {
    return ModernCard(
      hasGradient: true,
      padding: const EdgeInsets.all(AppTheme.spacingXL),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingL),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            ),
            child: const Icon(
              Icons.auto_fix_high,
              size: 64,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: AppTheme.spacingL),
          Text(
            l10n.appTitle,
            style: Theme.of(context).textTheme.displaySmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            l10n.selectImage,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureCards(BuildContext context, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '功能特色',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppTheme.spacingM),
        const Row(
          children: [
            Expanded(
              child: FeatureCard(
                icon: Icons.brush,
                title: '智能水印',
                description: '自动识别最佳位置',
                iconColor: AppTheme.primaryColor,
              ),
            ),
            SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: FeatureCard(
                icon: Icons.palette,
                title: '自定义样式',
                description: '多种字体和颜色',
                iconColor: AppTheme.secondaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingM),
        const Row(
          children: [
            Expanded(
              child: FeatureCard(
                icon: Icons.high_quality,
                title: '高清输出',
                description: '保持原图质量',
                iconColor: AppTheme.accentColor,
              ),
            ),
            SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: FeatureCard(
                icon: Icons.share,
                title: '快速分享',
                description: '一键保存分享',
                iconColor: AppTheme.warningColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context, AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '开始使用',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppTheme.spacingM),
        Row(
          children: [
            Expanded(
              child: ModernButton(
                text: l10n.camera,
                icon: Icons.camera_alt,
                type: ModernButtonType.gradient,
                onPressed: () => _pickImage(ImageSource.camera),
                isExpanded: true,
              ),
            ),
            const SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: ModernButton(
                text: l10n.gallery,
                icon: Icons.photo_library,
                type: ModernButtonType.outline,
                onPressed: () => _pickImage(ImageSource.gallery),
                isExpanded: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      // 检查权限
      bool hasPermission = await _checkPermissions(source);
      if (!hasPermission) return;

      final image = await _imageService.pickImage(source);
      if (image != null && mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WatermarkEditorScreen(imagePath: image.path),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.error),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<bool> _checkPermissions(ImageSource source) async {
    if (source == ImageSource.camera) {
      final status = await Permission.camera.request();
      if (status.isDenied) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.cameraPermission),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return false;
      }
    }
    return true;
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ImageSourceDialog(),
    );
  }
}