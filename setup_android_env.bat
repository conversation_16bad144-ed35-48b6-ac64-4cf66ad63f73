@echo off
echo Setting up Android development environment...

REM Check if Android Studio JDK exists
if exist "C:\Program Files\Android\Android Studio\jbr\bin\java.exe" (
    echo Found Android Studio JDK
    set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
    set PATH=%JAVA_HOME%\bin;%PATH%
    goto :found_java
)

REM Check if Eclipse Adoptium JDK exists
if exist "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe" (
    echo Found Eclipse Adoptium JDK 11
    set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
    set PATH=%JAVA_HOME%\bin;%PATH%
    goto :found_java
)

REM Check for other common JDK locations
for /d %%i in ("C:\Program Files\Java\jdk*") do (
    if exist "%%i\bin\java.exe" (
        echo Found JDK at %%i
        set JAVA_HOME=%%i
        set PATH=%JAVA_HOME%\bin;%PATH%
        goto :found_java
    )
)

echo ERROR: No Java JDK found!
echo Please install OpenJDK 11 from https://adoptium.net/
echo Or install Android Studio which includes a JDK
pause
exit /b 1

:found_java
echo JAVA_HOME set to: %JAVA_HOME%
java -version
echo.
echo Environment setup complete!
echo You can now run: flutter build apk
pause
