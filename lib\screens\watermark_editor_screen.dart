import 'dart:io';
import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import 'package:share_plus/share_plus.dart';
import '../services/image_service.dart';
import '../widgets/watermark_controls.dart';
import '../widgets/modern_card.dart';
import '../theme/app_theme.dart';

class WatermarkEditorScreen extends StatefulWidget {
  final String imagePath;

  const WatermarkEditorScreen({
    super.key,
    required this.imagePath,
  });

  @override
  State<WatermarkEditorScreen> createState() => _WatermarkEditorScreenState();
}

class _WatermarkEditorScreenState extends State<WatermarkEditorScreen> {
  final ImageService _imageService = ImageService();
  final TextEditingController _textController = TextEditingController();
  
  String? _processedImagePath;
  bool _isProcessing = false;
  
  // 水印参数
  String _watermarkText = '';
  double _opacity = 0.7;
  double _fontSize = 24.0;
  Offset _position = const Offset(0.1, 0.9);
  Color _textColor = Colors.white;
  WatermarkType _watermarkType = WatermarkType.text;

  @override
  void initState() {
    super.initState();
    _processedImagePath = widget.imagePath;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          l10n.addWatermark,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.save_outlined),
            onPressed: _isProcessing ? null : _saveImage,
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.successColor.withValues(alpha: 0.1),
              foregroundColor: AppTheme.successColor,
            ),
          ),
          const SizedBox(width: AppTheme.spacingS),
          IconButton(
            icon: const Icon(Icons.share_outlined),
            onPressed: _isProcessing ? null : _shareImage,
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
              foregroundColor: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(width: AppTheme.spacingM),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: Column(
          children: [
            // 图片预览区域
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                margin: const EdgeInsets.all(AppTheme.spacingM),
                child: ModernCard(
                  padding: EdgeInsets.zero,
                  isElevated: true,
                  borderRadius: AppTheme.radiusLarge,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                    child: _buildImagePreview(),
                  ),
                ),
            ),
          ),
          
          // 控制面板
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              child: ModernCard(
                child: WatermarkControls(
              watermarkType: _watermarkType,
              text: _watermarkText,
              opacity: _opacity,
              fontSize: _fontSize,
              position: _position,
              textColor: _textColor,
              onTypeChanged: (type) {
                setState(() {
                  _watermarkType = type;
                });
                _applyWatermark();
              },
              onTextChanged: (text) {
                setState(() {
                  _watermarkText = text;
                });
                _applyWatermark();
              },
              onOpacityChanged: (opacity) {
                setState(() {
                  _opacity = opacity;
                });
                _applyWatermark();
              },
              onFontSizeChanged: (fontSize) {
                setState(() {
                  _fontSize = fontSize;
                });
                _applyWatermark();
              },
              onPositionChanged: (position) {
                setState(() {
                  _position = position;
                });
                _applyWatermark();
              },
              onColorChanged: (color) {
                setState(() {
                  _textColor = color;
                });
                _applyWatermark();
              },
                ),
              ),
            ),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildImagePreview() {
    if (_isProcessing) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return GestureDetector(
      onTapDown: (details) {
        final RenderBox box = context.findRenderObject() as RenderBox;
        final Offset localPosition = box.globalToLocal(details.globalPosition);
        final Size size = box.size;
        
        setState(() {
          _position = Offset(
            localPosition.dx / size.width,
            localPosition.dy / size.height,
          );
        });
        _applyWatermark();
      },
      child: Stack(
        children: [
          Image.file(
            File(_processedImagePath!),
            fit: BoxFit.contain,
            width: double.infinity,
            height: double.infinity,
          ),
          if (_watermarkText.isNotEmpty)
            Positioned(
              left: _position.dx * MediaQuery.of(context).size.width * 0.8,
              top: _position.dy * MediaQuery.of(context).size.height * 0.4,
              child: Opacity(
                opacity: _opacity,
                child: Text(
                  _watermarkText,
                  style: TextStyle(
                    color: _textColor,
                    fontSize: _fontSize,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.5),
                        offset: const Offset(1, 1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _applyWatermark() async {
    if (_watermarkText.isEmpty) return;
    
    setState(() {
      _isProcessing = true;
    });

    try {
      File? result;
      
      switch (_watermarkType) {
        case WatermarkType.text:
          result = await _imageService.addTextWatermark(
            imagePath: widget.imagePath,
            text: _watermarkText,
            opacity: _opacity,
            fontSize: _fontSize,
            position: _position,
            textColor: _textColor,
          );
          break;
        case WatermarkType.date:
          result = await _imageService.addDateWatermark(
            imagePath: widget.imagePath,
            opacity: _opacity,
            fontSize: _fontSize,
            position: _position,
            textColor: _textColor,
          );
          break;
        default:
          break;
      }

      if (result != null) {
        setState(() {
          _processedImagePath = result!.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.error),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _saveImage() async {
    if (_processedImagePath == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final File? savedFile = await _imageService.saveToGallery(_processedImagePath!);
      
      if (savedFile != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.saved),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.error),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _shareImage() async {
    if (_processedImagePath == null) return;

    try {
      await Share.shareXFiles(
        [XFile(_processedImagePath!)],
        text: AppLocalizations.of(context)!.appTitle,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.error),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}

enum WatermarkType {
  text,
  date,
  logo,
  location,
}