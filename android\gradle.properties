# Gradle JVM settings - optimized for stability
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=1G -XX:ReservedCodeCacheSize=256m -XX:+HeapDumpOnOutOfMemoryError

# Android settings
android.useAndroidX=true
android.enableJetifier=true

# Enable incremental compilation for faster builds
kotlin.incremental=true
kotlin.incremental.android=true

# Enable Gradle caching for better performance
org.gradle.caching=true

# Enable parallel builds
org.gradle.parallel=true

# Enable configuration cache (Gradle 6.6+)
org.gradle.configuration-cache=true

# Disable unnecessary features that can cause issues
org.gradle.configureondemand=false
