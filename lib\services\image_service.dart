import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

class ImageService {
  final ImagePicker _picker = ImagePicker();

  Future<XFile?> pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 85,
      );
      return image;
    } catch (e) {
      print('Error picking image: $e');
      return null;
    }
  }

  Future<File?> addTextWatermark({
    required String imagePath,
    required String text,
    required double opacity,
    required double fontSize,
    required Offset position,
    Color textColor = Colors.white,
  }) async {
    try {
      // 读取原始图片
      final File imageFile = File(imagePath);
      final Uint8List imageBytes = await imageFile.readAsBytes();
      final img.Image? originalImage = img.decodeImage(imageBytes);
      
      if (originalImage == null) return null;

      // 创建带水印的图片
      final img.Image watermarkedImage = img.Image.from(originalImage);
      
      // 计算文字位置
      final int x = (position.dx * originalImage.width).round();
      final int y = (position.dy * originalImage.height).round();
      
      // 添加文字水印
      img.drawString(
        watermarkedImage,
        text,
        font: img.arial24,
        x: x,
        y: y,
        color: img.ColorRgba8(
          (textColor.red * 255).round(),
          (textColor.green * 255).round(),
          (textColor.blue * 255).round(),
          (opacity * 255).round(),
        ),
      );

      // 保存处理后的图片
      final Directory tempDir = await getTemporaryDirectory();
      final String fileName = 'watermarked_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final File outputFile = File('${tempDir.path}/$fileName');
      
      await outputFile.writeAsBytes(img.encodeJpg(watermarkedImage, quality: 90));
      
      return outputFile;
    } catch (e) {
      print('Error adding text watermark: $e');
      return null;
    }
  }

  Future<File?> addDateWatermark({
    required String imagePath,
    required double opacity,
    required double fontSize,
    required Offset position,
    Color textColor = Colors.white,
  }) async {
    final String dateText = DateTime.now().toString().split(' ')[0];
    return addTextWatermark(
      imagePath: imagePath,
      text: dateText,
      opacity: opacity,
      fontSize: fontSize,
      position: position,
      textColor: textColor,
    );
  }

  Future<File?> saveToGallery(String imagePath) async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String fileName = 'mirror_flower_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final File sourceFile = File(imagePath);
      final File savedFile = await sourceFile.copy('${appDocDir.path}/$fileName');
      
      return savedFile;
    } catch (e) {
      print('Error saving to gallery: $e');
      return null;
    }
  }
}